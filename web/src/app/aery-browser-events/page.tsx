"use client";

import { EventDetailsModal } from '@/components/aery-browser-events/EventDetailsModal';
import { EventStats as EventStatsComponent } from '@/components/aery-browser-events/EventStats';
import { formatDeviceDisplayName, formatUserDisplayName, getUserColor } from '@/components/aery-browser-events/utils';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import {
  AeryBrowserEvent,
  EVENT_TYPES,
  EventsResponse,
  isCreateAgentStepEvent,
  isOutputFileEvent,
  isSessionStartEvent,
  isTaskCreateEvent
} from '@/types/aery-browser-events';
import { Activity, Clock, Eye, FileText, Filter, Monitor, MousePointer, RefreshCw, Search, Trash2, Users } from 'lucide-react';
import { useEffect, useState } from 'react';

export default function AeryBrowserEventsPage() {
  const { toast } = useToast();
  const [events, setEvents] = useState<AeryBrowserEvent[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [selectedUserId, setSelectedUserId] = useState<string>('all');
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<AeryBrowserEvent | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Fetch events from the API
  const fetchEvents = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/v1/events');
      if (response.ok) {
        const data: EventsResponse = await response.json();
        setEvents(data.events || []);

        // Log informacion sobre tipos de eventos encontrados
        const eventTypes = data.events.reduce((acc, event) => {
          acc[event.event_type] = (acc[event.event_type] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);

        console.log('Tipos de eventos encontrados:', eventTypes);
        console.log('User IDs reales aplicados:', data.real_user_ids_applied);
      } else {
        throw new Error('Failed to fetch events');
      }
    } catch (error) {
      console.error('Error fetching events:', error);
      toast({
        title: "Error",
        description: "No se pudieron cargar los eventos",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch stats from the API (currently not used)
  const fetchStats = async () => {
    try {
      const response = await fetch('/api/v1/stats');
      if (response.ok) {
        const data = await response.json();
        // Stats functionality can be added later if needed
        console.log('Stats data:', data);
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  // Clear all events
  const clearEvents = async () => {
    try {
      const response = await fetch('/api/v1/events', { method: 'DELETE' });
      if (response.ok) {
        setEvents([]);
        toast({
          title: "Exito",
          description: "Todos los eventos han sido eliminados",
        });
      }
    } catch (error) {
      console.error('Error clearing events:', error);
      toast({
        title: "Error",
        description: "No se pudieron eliminar los eventos",
        variant: "destructive",
      });
    }
  };

  // Auto-refresh effect
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        fetchEvents();
        fetchStats();
      }, 5000);
      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  // Initial load
  useEffect(() => {
    fetchEvents();
    fetchStats();
  }, []);

  // Get unique user IDs for filter
  const uniqueUserIds = Array.from(new Set(events.map(event => String(event.data.user_id || '')).filter(Boolean)));

  // Filter events based on search and filters
  const filteredEvents = events.filter(event => {
    const matchesSearch = searchTerm === '' ||
      JSON.stringify(event).toLowerCase().includes(searchTerm.toLowerCase());

    const matchesType = selectedType === 'all' || String(event.event_type) === selectedType;

    const matchesUserId = selectedUserId === 'all' || String(event.data.user_id || '') === selectedUserId;

    return matchesSearch && matchesType && matchesUserId;
  });

  // Get unique event types for filter
  const uniqueEventTypes = Array.from(new Set(events.map(event => String(event.event_type || ''))));

  // Generate unique key for React components
  const generateEventKey = (event: AeryBrowserEvent, index: number) => {
    // Handle both string and object IDs
    const idStr = typeof event.id === 'string' ? event.id : JSON.stringify(event.id);
    // Use multiple fields to ensure uniqueness
    return `${idStr}-${event.event_id}-${event.data.created_at}-${index}`;
  };

  // Handle event selection
  const handleEventClick = (event: AeryBrowserEvent) => {
    setSelectedEvent(event);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedEvent(null);
  };

  // Get event type icon
  const getEventTypeIcon = (eventType: string) => {
    switch (eventType) {
      case EVENT_TYPES.SESSION_START:
      case 'SessionStartEvent':
      case 'CreateAgentSessionEvent':
        return <Monitor className="h-4 w-4" />;
      case EVENT_TYPES.TASK_CREATE:
      case 'TaskCreateEvent':
      case 'CreateAgentTaskEvent':
        return <Activity className="h-4 w-4" />;
      case EVENT_TYPES.CREATE_AGENT_STEP:
      case 'CreateAgentStepEvent':
        return <MousePointer className="h-4 w-4" />;
      case EVENT_TYPES.OUTPUT_FILE:
      case 'OutputFileEvent':
        return <FileText className="h-4 w-4" />;
      case EVENT_TYPES.UPDATE_TASK:
      case 'UpdateTaskEvent':
        return <RefreshCw className="h-4 w-4" />;
      default:
        return <Activity className="h-4 w-4" />;
    }
  };

  // Get event type color
  const getEventTypeColor = (eventType: string) => {
    switch (eventType) {
      case EVENT_TYPES.SESSION_START:
      case 'SessionStartEvent':
      case 'CreateAgentSessionEvent':
        return 'bg-blue-100 text-blue-800';
      case EVENT_TYPES.TASK_CREATE:
      case 'TaskCreateEvent':
      case 'CreateAgentTaskEvent':
        return 'bg-green-100 text-green-800';
      case EVENT_TYPES.CREATE_AGENT_STEP:
      case 'CreateAgentStepEvent':
        return 'bg-yellow-100 text-yellow-800';
      case EVENT_TYPES.OUTPUT_FILE:
      case 'OutputFileEvent':
        return 'bg-purple-100 text-purple-800';
      case EVENT_TYPES.UPDATE_TASK:
      case 'UpdateTaskEvent':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get event type label
  const getEventTypeLabel = (eventType: string) => {
    switch (eventType) {
      case EVENT_TYPES.SESSION_START:
      case 'SessionStartEvent':
      case 'CreateAgentSessionEvent':
        return 'Inicio de Sesion';
      case EVENT_TYPES.TASK_CREATE:
      case 'TaskCreateEvent':
      case 'CreateAgentTaskEvent':
        return 'Crear Tarea';
      case EVENT_TYPES.CREATE_AGENT_STEP:
      case 'CreateAgentStepEvent':
        return 'Paso del Agente';
      case EVENT_TYPES.OUTPUT_FILE:
      case 'OutputFileEvent':
        return 'Archivo de Salida';
      case EVENT_TYPES.UPDATE_TASK:
      case 'UpdateTaskEvent':
        return 'Actualizar Tarea';
      default:
        return eventType;
    }
  };

  // Get specific event details based on event type
  const getEventDetails = (event: AeryBrowserEvent) => {
    if (isCreateAgentStepEvent(event)) {
      return {
        title: `Paso ${event.data.step || 'N/A'}`,
        subtitle: event.data.agent_task_id ? `Tarea: ${event.data.agent_task_id}` : undefined,
        description: event.data.next_goal || event.data.memory || event.data.evaluation_previous_goal || 'Sin descripcion',
        metadata: [
          event.data.evaluation_previous_goal && `Evaluacion: ${event.data.evaluation_previous_goal.substring(0, 100)}...`,
          event.data.actions?.length && `${event.data.actions.length} accion(es)`,
          event.data.url && `URL: ${event.data.url}`
        ].filter(Boolean)
      };
    }

    if (isTaskCreateEvent(event) || event.event_type === 'CreateAgentTaskEvent') {
      return {
        title: 'Nueva Tarea',
        subtitle: event.data.agent_task_id || event.data.id || undefined,
        description: event.data.task || 'Sin descripcion de tarea',
        metadata: [
          event.data.llm_model && `Modelo: ${event.data.llm_model}`,
          event.data.agent_state && `Estado: ${event.data.agent_state}`
        ].filter(Boolean)
      };
    }

    if (isSessionStartEvent(event) || event.event_type === 'CreateAgentSessionEvent') {
      return {
        title: 'Sesion Iniciada',
        subtitle: event.data.browser_session_id || event.data.id || undefined,
        description: 'Nueva sesion de navegador iniciada',
        metadata: [
          event.data.browser_state?.viewport && `Viewport: ${event.data.browser_state.viewport.width}x${event.data.browser_state.viewport.height}`,
          event.data.browser_state?.url && `URL: ${event.data.browser_state.url}`,
          event.data.browser_state?.user_agent && `User Agent: ${event.data.browser_state.user_agent.substring(0, 50)}...`
        ].filter(Boolean)
      };
    }

    if (isOutputFileEvent(event)) {
      return {
        title: 'Archivo Generado',
        subtitle: event.data.file_path || undefined,
        description: `Archivo ${event.data.file_type || 'desconocido'} generado`,
        metadata: [
          event.data.file_size && `Tamano: ${(event.data.file_size / 1024).toFixed(2)} KB`
        ].filter(Boolean)
      };
    }

    // Fallback para eventos no tipados especificamente
    return {
      title: getEventTypeLabel(event.event_type),
      subtitle: event.data.id || undefined,
      description: event.data.task || event.data.description || 'Evento de aery-browser',
      metadata: [
        event.data.agent_task_id && `Task ID: ${event.data.agent_task_id}`,
        event.data.step && `Step: ${event.data.step}`
      ].filter(Boolean)
    };
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard de Eventos Aery-Browser</h1>
          <p className="text-muted-foreground">
            Monitorea eventos de aery-browser en tiempo real
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
            className={autoRefresh ? 'bg-green-50 border-green-200' : ''}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${autoRefresh ? 'animate-spin' : ''}`} />
            Auto-refresh {autoRefresh ? 'ON' : 'OFF'}
          </Button>
          <Button variant="outline" size="sm" onClick={fetchEvents} disabled={isLoading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Actualizar
          </Button>
          <Button variant="destructive" size="sm" onClick={clearEvents}>
            <Trash2 className="h-4 w-4 mr-2" />
            Limpiar
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      {events.length > 0 && <EventStatsComponent events={events} />}

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filtros y Busqueda
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Buscar en eventos..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger className="w-full sm:w-[200px]">
                <SelectValue placeholder="Tipo de evento" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos los tipos</SelectItem>
                {uniqueEventTypes.map((type) => (
                  <SelectItem key={type} value={type}>
                    {getEventTypeLabel(type)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedUserId} onValueChange={setSelectedUserId}>
              <SelectTrigger className="w-full sm:w-[200px]">
                <SelectValue placeholder="Usuario" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos los usuarios</SelectItem>
                {uniqueUserIds.map(userId => (
                  <SelectItem key={userId} value={userId}>
                    {formatUserDisplayName(userId)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button variant="outline" size="sm" onClick={() => {
              setSearchTerm('');
              setSelectedType('all');
              setSelectedUserId('all');
            }}>
              <span className="text-sm">✕</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Events List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Eventos ({filteredEvents.length})</span>
            {isLoading && <RefreshCw className="h-4 w-4 animate-spin" />}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[600px]">
            <div className="space-y-4">
              {filteredEvents.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  {events.length === 0 ? 'No hay eventos disponibles' : 'No se encontraron eventos con los filtros aplicados'}
                </div>
              ) : (
                filteredEvents.map((event, index) => {
                  const details = getEventDetails(event);
                  return (
                    <Card key={generateEventKey(event, index)} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <Badge className={getEventTypeColor(event.event_type)}>
                                {getEventTypeIcon(event.event_type)}
                                <span className="ml-1">{getEventTypeLabel(event.event_type)}</span>
                              </Badge>
                              <Badge variant="outline" className={getUserColor(event.data.user_id)}>
                                <Users className="h-3 w-3 mr-1" />
                                {formatUserDisplayName(event.data.user_id)}
                              </Badge>
                              {event.data.device_id && (
                                <Badge variant="outline">
                                  <Monitor className="h-3 w-3 mr-1" />
                                  {formatDeviceDisplayName(event.data.device_id)}
                                </Badge>
                              )}
                            </div>
                            <h3 className="font-semibold text-lg">{details.title}</h3>
                            {details.subtitle && (
                              <p className="text-sm text-muted-foreground mb-1">{details.subtitle}</p>
                            )}
                            <p className="text-sm mb-2">{details.description}</p>
                            {details.metadata.length > 0 && (
                              <div className="flex flex-wrap gap-1 mb-2">
                                {details.metadata.map((meta, index) => (
                                  <Badge key={`${meta}-${index}`} variant="secondary" className="text-xs">
                                    {meta}
                                  </Badge>
                                ))}
                              </div>
                            )}
                            <div className="flex items-center gap-2 text-xs text-muted-foreground">
                              <Clock className="h-3 w-3" />
                              <span>{formatTimestamp(event.data.created_at || event.timestamp || event.data.event_created_at)}</span>
                            </div>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEventClick(event)}
                            className="ml-4"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })
              )}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Event Details Modal */}
      <EventDetailsModal
        event={selectedEvent}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />
    </div>
  );
}